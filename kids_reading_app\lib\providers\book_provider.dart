import 'package:flutter/foundation.dart';
import '../models/book.dart';
import '../models/reading_progress.dart';
import '../services/book_service.dart';

class BookProvider with ChangeNotifier {
  final BookService _bookService = BookService();
  
  List<Book> _allBooks = [];
  List<Book> _recentBooks = [];
  Book? _currentBook;
  ReadingProgress? _currentProgress;
  bool _isLoading = false;
  
  // Getters
  List<Book> get allBooks => _allBooks;
  List<Book> get recentBooks => _recentBooks;
  Book? get currentBook => _currentBook;
  ReadingProgress? get currentProgress => _currentProgress;
  bool get isLoading => _isLoading;
  
  // 初始化，加载所有书籍
  Future<void> initialize() async {
    _setLoading(true);
    try {
      _allBooks = await _bookService.getAllBooks();
      _recentBooks = await _bookService.getRecentlyReadBooks();
      notifyListeners();
    } catch (e) {
      print('Error initializing books: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // 按分类获取书籍
  Future<List<Book>> getBooksByCategory(String category) async {
    return await _bookService.getBooksByCategory(category);
  }
  
  // 按年龄组获取书籍
  Future<List<Book>> getBooksByAgeGroup(int ageGroup) async {
    return await _bookService.getBooksByAgeGroup(ageGroup);
  }
  
  // 打开一本书
  Future<void> openBook(String bookId) async {
    _setLoading(true);
    try {
      _currentBook = await _bookService.getBookById(bookId);
      if (_currentBook != null) {
        _currentProgress = await _bookService.getReadingProgress(bookId);
        notifyListeners();
      }
    } catch (e) {
      print('Error opening book: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  // 更新阅读进度
  Future<void> updateReadingProgress({int? pageNumber, bool? isCompleted}) async {
    if (_currentBook == null || _currentProgress == null) return;
    
    final updatedProgress = _currentProgress!.copyWith(
      currentPage: pageNumber ?? _currentProgress!.currentPage,
      lastReadTime: DateTime.now(),
      isCompleted: isCompleted ?? _currentProgress!.isCompleted,
    );
    
    _currentProgress = updatedProgress;
    await _bookService.saveReadingProgress(updatedProgress);
    
    // 刷新最近阅读的书籍
    _recentBooks = await _bookService.getRecentlyReadBooks();
    
    notifyListeners();
  }
  
  // 添加书签
  Future<void> toggleBookmark(int pageNumber) async {
    if (_currentBook == null || _currentProgress == null) return;
    
    final bookmarks = List<int>.from(_currentProgress!.bookmarks);
    
    if (bookmarks.contains(pageNumber)) {
      bookmarks.remove(pageNumber);
    } else {
      bookmarks.add(pageNumber);
    }
    
    final updatedProgress = _currentProgress!.copyWith(
      bookmarks: bookmarks,
      lastReadTime: DateTime.now(),
    );
    
    _currentProgress = updatedProgress;
    await _bookService.saveReadingProgress(updatedProgress);
    
    notifyListeners();
  }
  
  // 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
}

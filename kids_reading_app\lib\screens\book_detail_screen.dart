import 'package:flutter/material.dart';
import '../models/book.dart';

class BookDetailScreen extends StatelessWidget {
  final Book book;

  const BookDetailScreen({super.key, required this.book});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(book.title),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 书籍封面和信息
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 封面图片占位符
                  Container(
                    width: 120,
                    height: 180,
                    color: Colors.blue[100],
                    child: const Center(
                      child: Icon(Icons.book, size: 48),
                    ),
                  ),
                  const SizedBox(width: 16),
                  // 书籍信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          book.title,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text('作者: ${book.author}'),
                        const SizedBox(height: 4),
                        Text('适合年龄: ${book.ageGroup}岁'),
                        const SizedBox(height: 4),
                        Text('阅读难度: ${book.readingLevel}/5'),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          children: book.categories.map((category) {
                            return Chip(
                              label: Text(category),
                              backgroundColor: Colors.blue[50],
                            );
                          }).toList(),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              // 书籍描述
              const Text(
                '简介',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(book.description),
              const SizedBox(height: 24),
              // 开始阅读按钮
              Center(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // 导航到阅读页面
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ReadingScreen(book: book),
                      ),
                    );
                  },
                  icon: const Icon(Icons.book),
                  label: const Text('开始阅读'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 32,
                      vertical: 12,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ReadingScreen extends StatefulWidget {
  final Book book;

  const ReadingScreen({super.key, required this.book});

  @override
  State<ReadingScreen> createState() => _ReadingScreenState();
}

class _ReadingScreenState extends State<ReadingScreen> {
  int _currentPage = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.book.title),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 页面内容
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Center(
                  child: Text(
                    widget.book.pages[_currentPage],
                    style: const TextStyle(fontSize: 20),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
            // 页面导航
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 上一页按钮
                  ElevatedButton(
                    onPressed: _currentPage > 0
                        ? () {
                            setState(() {
                              _currentPage--;
                            });
                          }
                        : null,
                    child: const Text('上一页'),
                  ),
                  // 页码
                  Text(
                    '${_currentPage + 1}/${widget.book.pages.length}',
                    style: const TextStyle(fontSize: 16),
                  ),
                  // 下一页按钮
                  ElevatedButton(
                    onPressed: _currentPage < widget.book.pages.length - 1
                        ? () {
                            setState(() {
                              _currentPage++;
                            });
                          }
                        : null,
                    child: const Text('下一页'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

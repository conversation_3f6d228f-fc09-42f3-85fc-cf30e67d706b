class Book {
  final String id;
  final String title;
  final String author;
  final String coverImage;
  final String description;
  final List<String> pages;
  final int ageGroup; // 例如：3-5岁，6-8岁等
  final List<String> categories;
  final int readingLevel; // 1-5，表示难度级别

  Book({
    required this.id,
    required this.title,
    required this.author,
    required this.coverImage,
    required this.description,
    required this.pages,
    required this.ageGroup,
    required this.categories,
    required this.readingLevel,
  });

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      id: json['id'],
      title: json['title'],
      author: json['author'],
      coverImage: json['coverImage'],
      description: json['description'],
      pages: List<String>.from(json['pages']),
      ageGroup: json['ageGroup'],
      categories: List<String>.from(json['categories']),
      readingLevel: json['readingLevel'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'coverImage': coverImage,
      'description': description,
      'pages': pages,
      'ageGroup': ageGroup,
      'categories': categories,
      'readingLevel': readingLevel,
    };
  }
}

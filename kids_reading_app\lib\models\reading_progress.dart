class ReadingProgress {
  final String bookId;
  final int currentPage;
  final DateTime lastReadTime;
  final bool isCompleted;
  final List<int> bookmarks;

  ReadingProgress({
    required this.bookId,
    required this.currentPage,
    required this.lastReadTime,
    required this.isCompleted,
    required this.bookmarks,
  });

  factory ReadingProgress.fromJson(Map<String, dynamic> json) {
    return ReadingProgress(
      bookId: json['bookId'],
      currentPage: json['currentPage'],
      lastReadTime: DateTime.parse(json['lastReadTime']),
      isCompleted: json['isCompleted'],
      bookmarks: List<int>.from(json['bookmarks']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'bookId': bookId,
      'currentPage': currentPage,
      'lastReadTime': lastReadTime.toIso8601String(),
      'isCompleted': isCompleted,
      'bookmarks': bookmarks,
    };
  }

  ReadingProgress copyWith({
    String? bookId,
    int? currentPage,
    DateTime? lastReadTime,
    bool? isCompleted,
    List<int>? bookmarks,
  }) {
    return ReadingProgress(
      bookId: bookId ?? this.bookId,
      currentPage: currentPage ?? this.currentPage,
      lastReadTime: lastReadTime ?? this.lastReadTime,
      isCompleted: isCompleted ?? this.isCompleted,
      bookmarks: bookmarks ?? this.bookmarks,
    );
  }
}

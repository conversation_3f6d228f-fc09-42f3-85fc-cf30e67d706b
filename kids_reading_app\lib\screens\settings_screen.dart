import 'package:flutter/material.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  double _fontSize = 18.0;
  bool _isDarkMode = false;
  bool _enableAnimations = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '阅读设置',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              // 字体大小设置
              Row(
                children: [
                  const Text('字体大小:'),
                  Expanded(
                    child: Slider(
                      value: _fontSize,
                      min: 12.0,
                      max: 32.0,
                      divisions: 10,
                      label: _fontSize.round().toString(),
                      onChanged: (value) {
                        setState(() {
                          _fontSize = value;
                        });
                      },
                    ),
                  ),
                  Text('${_fontSize.round()}'),
                ],
              ),
              const SizedBox(height: 16),
              // 深色模式开关
              SwitchListTile(
                title: const Text('深色模式'),
                value: _isDarkMode,
                onChanged: (value) {
                  setState(() {
                    _isDarkMode = value;
                  });
                },
              ),
              // 动画效果开关
              SwitchListTile(
                title: const Text('启用动画效果'),
                value: _enableAnimations,
                onChanged: (value) {
                  setState(() {
                    _enableAnimations = value;
                  });
                },
              ),
              const Divider(),
              const SizedBox(height: 16),
              const Text(
                '关于',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              ListTile(
                title: const Text('应用版本'),
                subtitle: const Text('1.0.0'),
                leading: const Icon(Icons.info),
              ),
              ListTile(
                title: const Text('联系我们'),
                subtitle: const Text('<EMAIL>'),
                leading: const Icon(Icons.email),
                onTap: () {
                  // 发送邮件的功能
                },
              ),
              ListTile(
                title: const Text('隐私政策'),
                leading: const Icon(Icons.privacy_tip),
                onTap: () {
                  // 显示隐私政策
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

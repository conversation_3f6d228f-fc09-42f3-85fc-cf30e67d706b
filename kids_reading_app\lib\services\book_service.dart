import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/book.dart';
import '../models/reading_progress.dart';

class BookService {
  static const String _booksKey = 'books';
  static const String _progressKey = 'reading_progress';

  // 获取所有书籍
  Future<List<Book>> getAllBooks() async {
    // 在实际应用中，这里可能会从API或本地数据库获取数据
    // 这里我们使用模拟数据
    return _getMockBooks();
  }

  // 根据分类获取书籍
  Future<List<Book>> getBooksByCategory(String category) async {
    final allBooks = await getAllBooks();
    return allBooks.where((book) => book.categories.contains(category)).toList();
  }

  // 根据年龄组获取书籍
  Future<List<Book>> getBooksByAgeGroup(int ageGroup) async {
    final allBooks = await getAllBooks();
    return allBooks.where((book) => book.ageGroup == ageGroup).toList();
  }

  // 获取书籍详情
  Future<Book?> getBookById(String id) async {
    final allBooks = await getAllBooks();
    try {
      return allBooks.firstWhere((book) => book.id == id);
    } catch (e) {
      return null;
    }
  }

  // 保存阅读进度
  Future<void> saveReadingProgress(ReadingProgress progress) async {
    final prefs = await SharedPreferences.getInstance();
    final progressList = await _getReadingProgressList();
    
    // 查找是否已有该书的阅读进度
    final index = progressList.indexWhere((p) => p.bookId == progress.bookId);
    
    if (index >= 0) {
      progressList[index] = progress;
    } else {
      progressList.add(progress);
    }
    
    await prefs.setString(_progressKey, jsonEncode(
      progressList.map((p) => p.toJson()).toList()
    ));
  }

  // 获取书籍的阅读进度
  Future<ReadingProgress?> getReadingProgress(String bookId) async {
    final progressList = await _getReadingProgressList();
    try {
      return progressList.firstWhere((p) => p.bookId == bookId);
    } catch (e) {
      // 如果没有找到，返回新的阅读进度
      return ReadingProgress(
        bookId: bookId,
        currentPage: 0,
        lastReadTime: DateTime.now(),
        isCompleted: false,
        bookmarks: [],
      );
    }
  }

  // 获取所有阅读进度
  Future<List<ReadingProgress>> _getReadingProgressList() async {
    final prefs = await SharedPreferences.getInstance();
    final progressJson = prefs.getString(_progressKey);
    
    if (progressJson == null) {
      return [];
    }
    
    final List<dynamic> decoded = jsonDecode(progressJson);
    return decoded.map((item) => ReadingProgress.fromJson(item)).toList();
  }

  // 获取最近阅读的书籍
  Future<List<Book>> getRecentlyReadBooks() async {
    final progressList = await _getReadingProgressList();
    final allBooks = await getAllBooks();
    
    // 按最后阅读时间排序
    progressList.sort((a, b) => b.lastReadTime.compareTo(a.lastReadTime));
    
    // 获取最近阅读的书籍ID列表
    final recentBookIds = progressList.map((p) => p.bookId).toList();
    
    // 按照阅读顺序返回书籍
    final recentBooks = <Book>[];
    for (final id in recentBookIds) {
      final book = allBooks.firstWhere((b) => b.id == id, orElse: () => allBooks[0]);
      recentBooks.add(book);
    }
    
    return recentBooks;
  }

  // 模拟数据
  List<Book> _getMockBooks() {
    return [
      Book(
        id: '1',
        title: '小红帽',
        author: '格林兄弟',
        coverImage: 'assets/images/red_riding_hood.png',
        description: '一个关于小女孩和大灰狼的经典童话故事。',
        pages: [
          '从前，有一个可爱的小女孩，她总是戴着奶奶送给她的红色小帽子，所以大家都叫她小红帽。',
          '有一天，妈妈对小红帽说："奶奶生病了，我烤了些饼干，你去看望奶奶吧。记住，不要在森林里逗留，也不要和陌生人说话。"',
          '小红帽答应了妈妈，拿着装满饼干的篮子出发了。',
          '在森林里，小红帽遇到了一只大灰狼。"你好，小姑娘，你要去哪里？"大灰狼问道。',
          '小红帽忘记了妈妈的嘱咐，回答说："我要去看望生病的奶奶，给她送饼干。"',
          '"你奶奶住在哪里？"大灰狼又问。小红帽指了指远处："就在森林那边的小屋里。"',
          '大灰狼想出了一个坏主意。他对小红帽说："看，这里有多漂亮的花啊！为什么不摘些花送给奶奶呢？"',
          '小红帽觉得这是个好主意，于是开始摘花。而大灰狼则跑到奶奶家，把奶奶吃掉了，然后穿上奶奶的衣服，躺在床上。',
          '当小红帽到达奶奶家时，她发现奶奶看起来有些奇怪。',
          '"奶奶，您的耳朵怎么这么大？"小红帽问道。',
          '"为了能更好地听到你的声音，亲爱的。"大灰狼回答。',
          '"奶奶，您的眼睛怎么这么大？"',
          '"为了能更好地看到你，亲爱的。"',
          '"奶奶，您的嘴巴怎么这么大？"',
          '"为了能更好地吃掉你！"大灰狼跳起来，向小红帽扑去。',
          '正好一个猎人经过，听到了喊叫声。他冲进屋子，救出了小红帽和奶奶。',
          '从此以后，小红帽再也不会在森林里和陌生人说话了。',
        ],
        ageGroup: 5,
        categories: ['童话', '经典'],
        readingLevel: 2,
      ),
      Book(
        id: '2',
        title: '三只小猪',
        author: '民间故事',
        coverImage: 'assets/images/three_pigs.png',
        description: '三只小猪建房子的故事，教导孩子勤劳和智慧的重要性。',
        pages: [
          '从前，有三只小猪兄弟。他们长大后，决定各自建造自己的房子。',
          '老大很懒，他用稻草很快搭建了一座房子。',
          '老二也不太勤快，他用木头建了一座房子，比稻草房结实一些。',
          '老三非常勤劳，他用砖头一块一块地建造了一座坚固的房子，花了很长时间。',
          '一天，大灰狼来了。他先来到老大的稻草房前。',
          '"小猪，小猪，让我进去！"大灰狼喊道。',
          '"不，不，不！"老大回答。',
          '"那我就吹倒你的房子！"大灰狼深吸一口气，用力一吹，稻草房子轻易就被吹倒了。',
          '老大赶紧跑到老二的木头房子里。',
          '大灰狼跟着来到木头房子前，又喊道："小猪，小猪，让我进去！"',
          '"不，不，不！"老大和老二一起回答。',
          '"那我就吹倒你的房子！"大灰狼又深吸一口气，用力吹，吹了好几次，终于把木头房子也吹倒了。',
          '两只小猪赶紧跑到老三的砖头房子里。',
          '大灰狼来到砖头房子前，再次喊道："小猪，小猪，让我进去！"',
          '"不，不，不！"三只小猪一起回答。',
          '"那我就吹倒你的房子！"大灰狼使出全身力气吹，但砖头房子纹丝不动。',
          '大灰狼想了想，决定从烟囱爬进去。但是老三早有准备，在壁炉里生了一锅热水。',
          '大灰狼从烟囱滑下来，掉进了热水锅里，烫得他嗷嗷叫，飞快地逃走了，再也没有回来。',
          '从此，三只小猪幸福地住在砖头房子里。他们明白了，只有勤劳和用心，才能建造坚固的家园。',
        ],
        ageGroup: 4,
        categories: ['童话', '寓言'],
        readingLevel: 1,
      ),
      Book(
        id: '3',
        title: '丑小鸭',
        author: '安徒生',
        coverImage: 'assets/images/ugly_duckling.png',
        description: '一个关于自我接纳和成长的感人故事。',
        pages: [
          '在一个美丽的夏日，鸭妈妈的蛋终于孵化了。小鸭子们一个接一个地破壳而出。',
          '但是，最后一个蛋里孵出的小鸭子看起来和其他的不一样——它又大又灰，一点也不可爱。',
          '农场里的所有动物都嘲笑它，连它的兄弟姐妹们也不和它一起玩。大家都叫它"丑小鸭"。',
          '丑小鸭非常难过，它决定离开农场，独自生活。',
          '秋天来了，丑小鸭在湖边遇到了一群美丽的天鹅。它非常羡慕它们，但不敢靠近。',
          '冬天很冷，丑小鸭几乎冻死在雪地里。幸运的是，一位老农夫发现了它，把它带回家照顾。',
          '但是农夫家的孩子们也取笑它，丑小鸭再次逃走了。',
          '它在一个洞穴里度过了剩下的冬天，非常孤独。',
          '春天来了，丑小鸭来到一个湖边。它看到湖面上有几只美丽的天鹅，心想："我要去见见这些美丽的鸟儿，即使它们会啄我，杀死我，也比被所有人嘲笑好。"',
          '当丑小鸭靠近水面时，它在水中看到了自己的倒影——它不再是那只丑陋的灰色小鸭子，而是变成了一只美丽的白天鹅！',
          '原来，它从一开始就是一只天鹅蛋，只是被放在了鸭妈妈的巢里。',
          '其他的天鹅欢迎它加入它们的行列。孩子们看到它时，都说："看，新来的那只是最美的一只天鹅！"',
          '曾经的丑小鸭，现在是最美丽的天鹅，它终于找到了自己的归属。',
        ],
        ageGroup: 6,
        categories: ['童话', '成长'],
        readingLevel: 3,
      ),
    ];
  }
}

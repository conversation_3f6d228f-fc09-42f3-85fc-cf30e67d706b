import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsProvider with ChangeNotifier {
  // 默认设置
  double _fontSize = 18.0;
  bool _isDarkMode = false;
  bool _enableAnimations = true;
  String _fontFamily = 'Roboto';
  
  // Getters
  double get fontSize => _fontSize;
  bool get isDarkMode => _isDarkMode;
  bool get enableAnimations => _enableAnimations;
  String get fontFamily => _fontFamily;
  
  // 主题数据
  ThemeData get lightTheme => ThemeData(
    primarySwatch: Colors.blue,
    brightness: Brightness.light,
    fontFamily: _fontFamily,
    textTheme: TextTheme(
      bodyLarge: TextStyle(fontSize: _fontSize),
      bodyMedium: TextStyle(fontSize: _fontSize - 2),
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.blue,
      foregroundColor: Colors.white,
    ),
  );
  
  ThemeData get darkTheme => ThemeData(
    primarySwatch: Colors.indigo,
    brightness: Brightness.dark,
    fontFamily: _fontFamily,
    textTheme: TextTheme(
      bodyLarge: TextStyle(fontSize: _fontSize),
      bodyMedium: TextStyle(fontSize: _fontSize - 2),
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.indigo,
      foregroundColor: Colors.white,
    ),
  );
  
  // 当前主题
  ThemeData get currentTheme => _isDarkMode ? darkTheme : lightTheme;
  
  // 初始化设置
  Future<void> loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    _fontSize = prefs.getDouble('fontSize') ?? 18.0;
    _isDarkMode = prefs.getBool('isDarkMode') ?? false;
    _enableAnimations = prefs.getBool('enableAnimations') ?? true;
    _fontFamily = prefs.getString('fontFamily') ?? 'Roboto';
    
    notifyListeners();
  }
  
  // 更新字体大小
  Future<void> setFontSize(double size) async {
    if (size < 12.0 || size > 32.0) return;
    
    _fontSize = size;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('fontSize', size);
    
    notifyListeners();
  }
  
  // 切换深色/浅色模式
  Future<void> toggleDarkMode() async {
    _isDarkMode = !_isDarkMode;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isDarkMode', _isDarkMode);
    
    notifyListeners();
  }
  
  // 切换动画效果
  Future<void> toggleAnimations() async {
    _enableAnimations = !_enableAnimations;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('enableAnimations', _enableAnimations);
    
    notifyListeners();
  }
  
  // 设置字体
  Future<void> setFontFamily(String fontFamily) async {
    _fontFamily = fontFamily;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('fontFamily', fontFamily);
    
    notifyListeners();
  }
}

// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:kids_reading_app/main.dart';

void main() {
  testWidgets('App renders correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const KidsReadingApp());

    // Verify that the app title is displayed
    expect(find.text('儿童阅读'), findsOneWidget);

    // Verify that welcome message is displayed
    expect(find.text('欢迎来到儿童阅读世界！'), findsOneWidget);

    // Verify that book section title is displayed
    expect(find.text('推荐书籍'), findsOneWidget);

    // Verify that some books are displayed
    expect(find.text('小红帽'), findsOneWidget);
    expect(find.text('三只小猪'), findsOneWidget);
  });
}

import 'package:flutter/material.dart';
import 'models/book.dart';
import 'services/book_service.dart';
import 'screens/book_detail_screen.dart';
import 'screens/settings_screen.dart';

void main() {
  runApp(const KidsReadingApp());
}

class KidsReadingApp extends StatelessWidget {
  const KidsReadingApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '儿童阅读',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
        scaffoldBackgroundColor: Colors.blue[50],
      ),
      home: const HomePage(),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final BookService _bookService = BookService();
  List<Book> _books = [];
  bool _isLoading = true;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _loadBooks();
  }

  Future<void> _loadBooks() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final books = await _bookService.getAllBooks();
      setState(() {
        _books = books;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // 使用日志记录错误，而不是print
      debugPrint('Error loading books: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('儿童阅读'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: SafeArea(
        child:
            _currentIndex == 0 ? _buildHomeContent() : const SettingsScreen(),
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: '首页',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: '设置',
          ),
        ],
      ),
    );
  }

  Widget _buildHomeContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '欢迎来到儿童阅读世界！',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 20),
          const Text(
            '推荐书籍',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 10),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 10,
              mainAxisSpacing: 10,
              children: _books.map((book) {
                return _buildBookCard(
                  book,
                  _getColorForBook(book.id),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Color _getColorForBook(String bookId) {
    final colors = [
      Colors.red[100]!,
      Colors.pink[100]!,
      Colors.purple[100]!,
      Colors.blue[100]!,
      Colors.green[100]!,
      Colors.orange[100]!,
    ];

    // 根据书籍ID选择颜色
    final colorIndex = int.parse(bookId) % colors.length;
    return colors[colorIndex];
  }

  Widget _buildBookCard(Book book, Color color) {
    return Card(
      color: color,
      elevation: 4,
      child: InkWell(
        onTap: () {
          // 导航到书籍详情页面
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => BookDetailScreen(book: book),
            ),
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.book, size: 48),
              const SizedBox(height: 8),
              Text(
                book.title,
                style:
                    const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                book.author,
                style: const TextStyle(fontSize: 12),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
